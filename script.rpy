# Вы можете расположить сценарий своей игры в этом файле.

# Определение персонажей игры.
define e = Character('Эйлин', color="#c8ffc8")

# Вместо использования оператора image можете просто
# складывать все ваши файлы изображений в папку images.
# Например, сцену bg room можно вызвать файлом "bg room.png",
# а eileen happy — "eileen happy.webp", и тогда они появятся в игре.


# Игра начинается здесь:
label start:

    
    screen ui_button_overlay():
        style_prefix "ui_button"
        zorder 100
        
        textbutton _("UI"):
            action ShowMenu("ui")
            align (0.5, 0.5)
        textbutton _("Anomaly"):
            action Jump("anomaly")
            align (0.5, 0.53)
        textbutton _("Relationship"):
            action Jump("relationship")
            align (0.5, 0.56)
        textbutton _("Block"):
            action Jump("block")
            align (0.5, 0.59)
        textbutton _("Mission"):
            action Jump("mission")
            align (0.5, 0.62)
        textbutton _("Inventory"):
            action Jump("inventory")
            align (0.5, 0.65)
        

    show screen ui_button_overlay

    scene black
    ""

    return

label anomaly:
    hide screen ui_button_overlay
    call screen anomaly

label block:
    hide screen ui_button_overlay
    call screen block

label mission:
    hide screen ui_button_overlay
    call screen mission

label inventory:
    hide screen ui_button_overlay
    call screen inventory1

