# Define variables to track states
default persistent.is_muted = False
default persistent.active_location = "supplies1"
default persistent.time_of_day = "day"  # Options: day, night, morning, noon

# Define a function to toggle mute state
define gui.toggle_mute = Function(lambda: setattr(persistent, 'is_muted', not persistent.is_muted))

# Define transforms for smooth transitions
transform icon_transition:
    on show:
        alpha 0.0
        easein 0.3 alpha 1.0
    on replace:
        alpha 0.0
        easein 0.3 alpha 1.0
        
# Time of day transition animation
transform time_switch:
    on replace:
        alpha 0.0
        yoffset 10
        easein 0.3 alpha 1.0 yoffset 0
    on show:
        alpha 0.0
        yoffset 10
        easein 0.3 alpha 1.0 yoffset 0
        
# Parallax effect function for background
init python:
    def parallax_effect(trans, st, at):
        x, y = renpy.get_mouse_pos()
        offset_x = (x / config.screen_width - 0.5) * 40
        offset_y = (y / config.screen_height - 0.5) * 40
        trans.xoffset = offset_x
        trans.yoffset = offset_y
        return 0.0

# Transform for parallax background
transform parallax_bg:
    subpixel True
    zoom 1.05  # Slightly zoomed to allow movement without showing edges
    xalign 0.5
    yalign 0.5
    xoffset 0 yoffset 0
    function parallax_effect
    
# Transform for active/inactive location buttons
transform active_button:
    alpha 1.0
    on hover:
        ease 0.2 alpha 0.7
    on idle:
        ease 0.2 alpha 1.0
        
transform inactive_button:
    alpha 0.5
    on hover:
        ease 0.2 alpha 0.7
    on idle:
        ease 0.2 alpha 0.5

# Simple custom scrollbar styles
style custom_viewport is viewport

style custom_hscrollbar is hscrollbar:
    unscrollable "hide"
    ysize 2  # Height of the scrollbar
    top_margin 15
    bottom_margin 5
    xsize 890  # Width of the scrollbar
    left_bar "assets/ui/images/scroll_b.png"
    right_bar "assets/ui/images/scroll_b.png"

style custom_hthumb is hthumb:
    thumb "assets/ui/images/scroll_h.png"
    hover_thumb Transform("assets/ui/images/scroll_h.png", alpha=0.8)
    thumb_shadow None
    thumb_offset 0

screen ui():
    tag menu
    
    # Background with parallax effect
    add "assets/ui/location/FrontierBlock/background.png" at parallax_bg
    use top()
    button:
        background "assets/ui/images/raid.png"
        add "assets/ui/images/raid_dots.png" align (0.5, 0.5)
        xysize (146, 146)
        xpos 489 ypos 546
        action Null()
        vbox:
            align (0.5, 0.5)
            spacing 10
            add "assets/ui/images/raid_up.png" xalign 0.5 
            text _("GO TO RAID") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#C2C2C2"
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7

        
    add "assets/ui/images/character_dots.png" xpos 1006 ypos 658
        
    button:
        hbox:
            spacing 15
            add "assets/ui/images/ch_1.png"
            text _("MIRA") size 24 font "fonts/Montserrat-Bold.ttf" yalign 0.5 color "#C2C2C2"
        xysize (137, 63)
        xpos 1022 ypos 682
        action Null()
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7




    # Horizontal scrolling viewport for location buttons using the suggested approach
    frame:
        style "location_scroll_frame"
        background None
        xysize (1000, 130)  # Height for buttons plus scrollbar space
        ypos 925  # Adjusted vertical position
        xpos 31  # Center horizontally on screen
        
        # Viewport with hidden scrollbar but still scrollable
        viewport id "locations_scroll":
            draggable True  # Allow dragging to scroll
            mousewheel True  # Allow mousewheel scrolling
            scrollbars None  # Hide scrollbars completely
            ysize 97   # Exact height of buttons
            xsize 890  # Width of visible area
            xalign 0.5  # Center the viewport horizontally
            yalign 0.0  # Align to top of frame
            
            # No spacing needed since scrollbar is hidden
            yfill False
            
            # Container for buttons with horizontal layout
            hbox:
                spacing 20  # Space between buttons
                yalign 0.5  # Center vertically
                
                # Supplies1 button
                button:
                    background ("assets/ui/images/select_location1.png" if persistent.active_location == "supplies1" else "assets/ui/images/location1.png")
                    text _("Supplies") size 16 font "fonts/Montserrat-SemiBold.ttf" align (0.05, 0.85) color "#C2C2C2"
                    xysize (228, 97)
                    action SetVariable("persistent.active_location", "supplies1")
                    at (active_button if persistent.active_location == "supplies1" else inactive_button)
                
                # Outdoor button
                button:
                    background ("assets/ui/images/select_location1.png" if persistent.active_location == "outdoor" else "assets/ui/images/location1.png")
                    text _("Outdoor") size 16 font "fonts/Montserrat-SemiBold.ttf" align (0.05, 0.85) color "#C2C2C2"
                    xysize (228, 97)
                    action SetVariable("persistent.active_location", "outdoor")
                    at (active_button if persistent.active_location == "outdoor" else inactive_button)
                
                # Shop button
                button:
                    background ("assets/ui/images/select_location1.png" if persistent.active_location == "shop" else "assets/ui/images/location1.png")
                    text _("Shop") size 16 font "fonts/Montserrat-SemiBold.ttf" align (0.05, 0.85) color "#C2C2C2"
                    xysize (228, 97)
                    action SetVariable("persistent.active_location", "shop")
                    at (active_button if persistent.active_location == "shop" else inactive_button)
                
                # Supplies2 button
                button:
                    background ("assets/ui/images/select_location1.png" if persistent.active_location == "supplies2" else "assets/ui/images/location1.png")
                    text _("Supplies") size 16 font "fonts/Montserrat-SemiBold.ttf" align (0.05, 0.85) color "#C2C2C2"
                    xysize (228, 97)
                    action SetVariable("persistent.active_location", "supplies2")
                    at (active_button if persistent.active_location == "supplies2" else inactive_button)
                
                # Supplies2 button
                button:
                    background ("assets/ui/images/select_location1.png" if persistent.active_location == "supplies2" else "assets/ui/images/location1.png")
                    text _("Supplies") size 16 font "fonts/Montserrat-SemiBold.ttf" align (0.05, 0.85) color "#C2C2C2"
                    xysize (228, 97)
                    action SetVariable("persistent.active_location", "supplies2")
                    at (active_button if persistent.active_location == "supplies2" else inactive_button)
                
                # Room for additional location buttons
                # Just add more buttons here and they'll automatically be part of the scrollable area
    
    

    button:
        background "assets/ui/images/backmap.png"
        hbox:
            xalign 0.08
            yalign 0.85
            spacing 10
            add "assets/ui/images/location_icon.svg"
            add "assets/ui/images/line_location.png" yalign 0.5
            text _("Base map") size 16 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#C2C2C2"
        xysize (215, 97)
        xpos 1674 ypos 945
        action Null()
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7    
init python:
    def toggle_mission():
        if renpy.get_screen("mission") is None:
            renpy.show_screen("mission")
        else:
            renpy.hide_screen("mission")

screen top():
    
    # Add a transparent button for mouse tracking (helps with parallax)
    button:
        background "#0000"  # Fully transparent
        xysize (config.screen_width, config.screen_height)
        hovered NullAction()
        
    # Overlay with blur
    add "assets/ui/images/black_blur.png"

    frame:
        background "assets/ui/images/day_frame.svg"
        xysize (110, 48)
        xpos 31 ypos 31
        hbox:
            align (0.5, 0.5)
            spacing 5
            add "assets/ui/images/icon_day.png"
            text _("Day 33") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#c4c4c4"

    # Time of day switcher - clean implementation with fixed positions
    button:
        background "assets/ui/images/time_frame.svg"
        hover_background Transform("assets/ui/images/time_frame.svg", matrixcolor=BrightnessMatrix(0.1))
        xysize (139, 48)
        xpos 151 ypos 31
        action Function(lambda: setattr(persistent, 'time_of_day', {'day': 'night', 'night': 'morning', 'morning': 'noon', 'noon': 'day'}[persistent.time_of_day]))
        tooltip _({"day": "Switch to night", "night": "Switch to morning", "morning": "Switch to noon", "noon": "Switch to day"}[persistent.time_of_day])
        
        # Icon (left side) with animation
        add "assets/ui/images/" + persistent.time_of_day + ".png":
            xpos 12  # Fixed distance from left edge
            yalign 0.5
            at time_switch
        
        # Line (center) - constant element, no animation needed
        add "assets/ui/images/line_time.png":
            xpos 40  # Fixed position between icon and text
            yalign 0.5
            
        # Text (right side) with animation
        text _({"day": "Day", "night": "Night", "morning": "Morning", "noon": "Noon"}[persistent.time_of_day]):
            xpos 50  # Fixed position after the line
            yalign 0.5
            size 14
            font "fonts/Montserrat-SemiBold.ttf"
            color "#c4c4c4"
            at time_switch
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7
        
    add "assets/ui/images/line_left.png" xpos 310 ypos 46 

    frame:
        background "assets/ui/images/money_frame.svg"
        xysize (95, 48)
        xpos 332 ypos 31
        text _("$ 1000") size 14 font "fonts/Montserrat-SemiBold.ttf" align (0.5, 0.5) color "#20ADA3"


    button:
        background "assets/ui/images/quest_frame.svg"
        xysize (134, 48)
        xpos 893 ypos 31
        action Function(toggle_mission)
    
        hbox:
            align (0.5, 0.5)
            spacing 9
            hbox:
                spacing 3
                add "assets/ui/images/icon_quest.png"
                text _("30") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#c4c4c4"
            add "assets/ui/images/line_time.png" yalign 0.5
            text _("Quest") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#c4c4c4"
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7
                
    button:
        background "assets/ui/images/real_frame.svg"
        hover_background Transform("assets/ui/images/real_frame.svg", matrixcolor=BrightnessMatrix(0.1))
        xysize (167, 48)
        xpos 1477 ypos 31
        action Null()
        hbox:
            align (0.5, 0.5)
            spacing 10
            add "assets/ui/images/icon_real.png"
            add "assets/ui/images/line_real.png" yalign 0.5
            text _("Relationship") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#F65151"
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7

    button:
        background "assets/ui/images/invet_frame.svg"
        hover_background Transform("assets/ui/images/invet_frame.svg", matrixcolor=BrightnessMatrix(0.1))
        xysize (145, 48)
        xpos 1654 ypos 31
        action Null()
        hbox:
            align (0.5, 0.5)
            spacing 10
            add "assets/ui/images/icon_invet.png"
            add "assets/ui/images/line_invet.png" yalign 0.5
            text _("Inventory") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#C2C2C2"
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7

    add "assets/ui/images/line_left.png" xpos 1819 ypos 46 

    button:
        background ("assets/ui/images/unmute_icon.svg" if persistent.is_muted else "assets/ui/images/mute_icon.svg")
        hover_background Transform(("assets/ui/images/unmute_icon.svg" if persistent.is_muted else "assets/ui/images/mute_icon.svg"), matrixcolor=BrightnessMatrix(0.1))
        xpos 1841 ypos 31
        xysize (48, 48)
        action [gui.toggle_mute, ToggleMute("music")]
        tooltip _(("Unmute" if persistent.is_muted else "Mute") + " audio")
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7
        