
init python:
    # Mission status types
    MISSION_OUTGOING = "outgoing"    # Available but not started
    MISSION_ACTIVE = "active"        # Currently in progress
    MISSION_COMPLETED = "completed"  # Successfully finished
    MISSION_FAILED = "failed"        # Failed or abandoned






    # Mission data structures
    main_missions = [
        {
            "id": "way_home",
            "title": "Way Home",
            "description": "Find your way back to the lighthouse and discover the truth about your past.",
            "details": "You must navigate through the dangerous territories and uncover the mysteries that led you here. The path home is treacherous, but it's the only way to find answers.",
            "image": "images/test.png",
            "status": MISSION_ACTIVE,
            "expanded": False
        },
        {
            "id": "go_to_park",
            "title": "Go to Park",
            "description": "Investigate the strange occurrences in the abandoned park.",
            "details": "Reports of unusual activities have been coming from the old park. Strange lights and sounds have been witnessed by locals. You need to investigate what's happening there.",
            "image": "images/test.png",
            "status": MISSION_OUTGOING,
            "expanded": False
        }
    ]

    side_missions = [
        {
            "id": "get_food",
            "title": "Get Food",
            "description": "Find supplies to sustain yourself on the journey.",
            "image": "images/test.png",
            "status": MISSION_ACTIVE
        },
        {
            "id": "go_to_shower",
            "title": "Go to Shower",
            "description": "Clean yourself before continuing the mission.",
            "image": "images/test.png",
            "status": MISSION_OUTGOING
        }
    ]








    def toggle_mission_expansion(mission_id):
        """Toggle the expanded state of a main mission"""
        for mission in main_missions:
            if mission["id"] == mission_id:
                mission["expanded"] = not mission["expanded"]
                break

    def change_mission_status(mission_id, new_status, is_main=True):
        """Change mission status"""
        missions = main_missions if is_main else side_missions
        for mission in missions:
            if mission["id"] == mission_id:
                mission["status"] = new_status
                if is_main:
                    mission["expanded"] = False  # Collapse when status changes
                break

    def get_missions_by_status(missions, status):
        """Get missions filtered by status"""
        return [m for m in missions if m["status"] == status]

    def get_active_missions(missions):
        """Get only active missions"""
        return get_missions_by_status(missions, MISSION_ACTIVE)

    def get_outgoing_missions(missions):
        """Get only outgoing missions"""
        return get_missions_by_status(missions, MISSION_OUTGOING)

    # Debug variables
    debug_mode = False

    def toggle_debug_mode():
        """Toggle debug mode on/off"""
        global debug_mode
        debug_mode = not debug_mode
        renpy.notify("Debug mode: " + ("ON" if debug_mode else "OFF"))

    def debug_set_mission_status(mission_id, status, is_main=True):
        """Debug function to set mission status"""
        change_mission_status(mission_id, status, is_main)
        status_names = {
            MISSION_OUTGOING: "Outgoing",
            MISSION_ACTIVE: "Active",
            MISSION_COMPLETED: "Completed",
            MISSION_FAILED: "Failed"
        }
        renpy.notify(f"Mission {mission_id} set to {status_names.get(status, status)}!")

    def debug_complete_mission(mission_id, is_main=True):
        """Debug function to complete a mission"""
        debug_set_mission_status(mission_id, MISSION_COMPLETED, is_main)

    def debug_activate_mission(mission_id, is_main=True):
        """Debug function to activate a mission"""
        debug_set_mission_status(mission_id, MISSION_ACTIVE, is_main)

    def debug_fail_mission(mission_id, is_main=True):
        """Debug function to fail a mission"""
        debug_set_mission_status(mission_id, MISSION_FAILED, is_main)

    def debug_reset_mission(mission_id, is_main=True):
        """Debug function to reset a mission to outgoing"""
        debug_set_mission_status(mission_id, MISSION_OUTGOING, is_main)

    def debug_complete_all_main():
        """Debug function to complete all main missions"""
        for mission in main_missions:
            mission["status"] = MISSION_COMPLETED
        renpy.notify("All main missions completed!")

    def debug_complete_all_side():
        """Debug function to complete all side missions"""
        for mission in side_missions:
            mission["status"] = MISSION_COMPLETED
        renpy.notify("All side missions completed!")

    def debug_reset_all_missions():
        """Debug function to reset all missions to outgoing"""
        for mission in main_missions:
            mission["status"] = MISSION_OUTGOING
            mission["expanded"] = False
        for mission in side_missions:
            mission["status"] = MISSION_OUTGOING
        renpy.notify("All missions reset to outgoing!")

    def debug_add_main_mission():
        """Debug function to add a new main mission"""
        new_id = f"debug_main_{len(main_missions) + 1}"
        main_missions.append({
            "id": new_id,
            "title": f"Debug Main Mission {len(main_missions) + 1}",
            "description": "This is a debug mission created for testing purposes.",
            "details": "This mission was created using the debug system. It can be used to test the mission interface and functionality.",
            "image": "images/test.png",
            "status": MISSION_OUTGOING,
            "expanded": False
        })
        renpy.notify("New main mission added!")

    def debug_add_side_mission():
        """Debug function to add a new side mission"""
        new_id = f"debug_side_{len(side_missions) + 1}"
        side_missions.append({
            "id": new_id,
            "title": f"Debug Side Mission {len(side_missions) + 1}",
            "description": "This is a debug side mission created for testing purposes.",
            "image": "images/test.png",
            "status": MISSION_OUTGOING
        })
        renpy.notify("New side mission added!")

# Animation transforms for smooth expansion
transform quest_expand:
    alpha 0.0 yoffset -20
    easein 0.3 alpha 1.0 yoffset 0

transform quest_collapse:
    alpha 1.0 yoffset 0
    easeout 0.2 alpha 0.0 yoffset -20

transform quest_button_hover:
    on idle:
        easein 0.2 alpha 1.0
    on hover:
        easein 0.2 alpha 0.8

screen mission:

    # Mission tab selection
    default current_tab = "active"

    # Main container with structure matching the image
    frame:
        #background None  # Darker transparent background with rounded corners
        background "gui/mission/bg.png"
        xalign 0.5
        yalign 0.5
        xsize 1400
        ysize 800
        padding (40, 40)

        vbox:
            spacing 20
            xfill True
            yfill True

            # Top title section (like INVENTORY in the image)
            frame:
                background None # Darker transparent background with rounded corners
                xalign 0.5
                xysize (400, 80)
                padding (20, 20)

                
                frame:
                    xalign 0.5
                    xysize (214, 65)
                    background "assets/settings/images/logo.png"
                frame:
                    xalign 0.5
                    background "assets/settings/images/logo.png"
                    xysize (214, 65)
                    text _("MISSION") align (0.5, 0.5) size 16 font "fonts/Montserrat-Black.ttf"


            # Tab navigation section (horizontal layout like in image)
            frame:
                background None
                xalign 0.5  # Center the entire tab frame
                xysize (600, 60)  # Fixed width to center properly
                padding (20, 10)

                hbox:
                    spacing 17 # Smaller spacing between buttons
                    xalign 0.5  # Center the button group
                    yalign 0.5

                    # Active tab
                    button:
                        background Frame("gui/mission/layer 2.png", 10, 10, tile=False)
                        hover_background Frame("gui/mission/layer 2.png", 10, 10, tile=False)
                        xysize (140, 35)  # More compressed size
                        action SetScreenVariable("current_tab", "active")
                        at quest_button_hover

                        text "Active" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#2196F3" align (0.5, 0.5)

                    # Completed tab
                    button:
                        background Frame("gui/mission/layer 2.png", 10, 10, tile=False)
                        hover_background Frame("gui/mission/layer 2.png", 10, 10, tile=False)
                        xysize (140, 35)  # More compressed size
                        action SetScreenVariable("current_tab", "completed")
                        at quest_button_hover

                        text "Completed" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#FF9800" align (0.5, 0.5)

                    # Failed tab
                    button:
                        background Frame("gui/mission/layer 2.png", 10, 10, tile=False)
                        hover_background Frame("gui/mission/layer 2.png", 10, 10, tile=False)
                        xysize (140, 35)  # More compressed size
                        action SetScreenVariable("current_tab", "failed")
                        at quest_button_hover

                        text "Failed" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#F44336" align (0.5, 0.5)

            # Main content area (side-by-side layout like in image)
            frame:
                background None  # Darker transparent background with rounded corners
                xfill True
                yfill True
                padding (20, 20)

                hbox:
                    spacing 20
                    xfill True
                    yfill True

                    # Primary Objectives (left side)
                    vbox:
                        spacing 15
                        xsize 650
                        yfill True

                        # Primary Objectives header
                        frame:
                            background "#1f1f1fE0"  # Darker transparent header background with rounded corners
                            xfill True
                            ysize 50
                            padding (20, 10)

                            hbox:
                                spacing 10
                                yalign 0.5
                                add "assets/ui/images/icon_quest.png" yalign 0.5
                                text "Primary Objectives" size 18 font "fonts/Montserrat-Black.ttf" color "#ffffff" yalign 0.5

                        # Primary missions content
                        if current_tab == "active":
                            use quest_content_section(get_active_missions(main_missions), True)
                        elif current_tab == "completed":
                            use quest_content_section(get_missions_by_status(main_missions, MISSION_COMPLETED), True)
                        elif current_tab == "failed":
                            use quest_content_section(get_missions_by_status(main_missions, MISSION_FAILED), True)

                    # Secondary Objectives (right side)
                    vbox:
                        spacing 15
                        xsize 650
                        yfill True

                        # Secondary Objectives header
                        frame:
                            background "#1f1f1fE0"  # Darker transparent header background with rounded corners
                            xfill True
                            ysize 50
                            padding (20, 10)

                            hbox:
                                spacing 10
                                yalign 0.5
                                add "assets/ui/images/icon_quest.png" yalign 0.5
                                text "Secondary Objectives" size 18 font "fonts/Montserrat-Black.ttf" color "#ffffff" yalign 0.5

                        # Secondary missions content
                        if current_tab == "active":
                            use quest_content_section(get_active_missions(side_missions), False)
                        elif current_tab == "completed":
                            use quest_content_section(get_missions_by_status(side_missions, MISSION_COMPLETED), False)
                        elif current_tab == "failed":
                            use quest_content_section(get_missions_by_status(side_missions, MISSION_FAILED), False)



    # Debug toggle button with rounded corners and darker styling
    button:
        background ("#FF0000F0" if debug_mode else "#222222D0")
        hover_background ("#FF3333F0" if debug_mode else "#333333D0")
        xysize (80, 30)
        xalign 0.98
        yalign 0.02
        action Function(toggle_debug_mode)
        at quest_button_hover

        text "DEBUG" size 12 font "fonts/Montserrat-SemiBold.ttf" color "#ffffff" align (0.5, 0.5)

    # Debug panel with rounded corners and darker styling
    if debug_mode:
        frame:
            background "#1f1f1fF0"  # Darker transparent background with rounded corners
            xalign 0.02
            yalign 0.02
            xsize 300
            padding (20, 20)
            at quest_expand

            vbox:
                spacing 12
                text "DEBUG PANEL" size 16 font "fonts/Montserrat-Black.ttf" color "#FF0000"
                text "Mission Control" size 12 font "fonts/Montserrat-SemiBold.ttf" color "#C2C2C2"

                # Global controls with modern buttons
                vbox:
                    spacing 8
                    button:
                        background "#4CAF50"
                        hover_background "#66BB6A"
                        xysize (260, 30)
                        action Function(debug_complete_all_main)
                        text "Complete All Main" size 12 font "fonts/Montserrat-SemiBold.ttf" color "#ffffff" align (0.5, 0.5)

                    button:
                        background "#4CAF50"
                        hover_background "#66BB6A"
                        xysize (260, 30)
                        action Function(debug_complete_all_side)
                        text "Complete All Side" size 12 font "fonts/Montserrat-SemiBold.ttf" color "#ffffff" align (0.5, 0.5)

                    button:
                        background "#FF5722"
                        hover_background "#FF7043"
                        xysize (260, 30)
                        action Function(debug_reset_all_missions)
                        text "Reset All Missions" size 12 font "fonts/Montserrat-SemiBold.ttf" color "#ffffff" align (0.5, 0.5)

                    hbox:
                        spacing 5
                        button:
                            background "#2196F3"
                            hover_background "#42A5F5"
                            xysize (125, 30)
                            action Function(debug_add_main_mission)
                            text "Add Main" size 10 font "fonts/Montserrat-SemiBold.ttf" color "#ffffff" align (0.5, 0.5)

                        button:
                            background "#2196F3"
                            hover_background "#42A5F5"
                            xysize (125, 30)
                            action Function(debug_add_side_mission)
                            text "Add Side" size 10 font "fonts/Montserrat-SemiBold.ttf" color "#ffffff" align (0.5, 0.5)

                # Individual mission controls
                text "Individual Controls:" size 10 font "fonts/Montserrat-SemiBold.ttf" color "#C2C2C2"

                # Main missions debug controls
                for mission in main_missions:
                    $ short_title = mission["title"][:20] + "..." if len(mission["title"]) > 20 else mission["title"]
                    vbox:
                        spacing 3
                        text short_title size 9 color "#ffffff"
                        hbox:
                            spacing 3
                            button:
                                background "#4CAF50"
                                hover_background "#66BB6A"
                                xysize (25, 20)
                                action Function(debug_activate_mission, mission["id"], True)
                                text "A" size 8 color "#ffffff" align (0.5, 0.5)
                            button:
                                background "#FF9800"
                                hover_background "#FFB74D"
                                xysize (25, 20)
                                action Function(debug_complete_mission, mission["id"], True)
                                text "C" size 8 color "#ffffff" align (0.5, 0.5)
                            button:
                                background "#F44336"
                                hover_background "#EF5350"
                                xysize (25, 20)
                                action Function(debug_fail_mission, mission["id"], True)
                                text "F" size 8 color "#ffffff" align (0.5, 0.5)
                            button:
                                background "#9E9E9E"
                                hover_background "#BDBDBD"
                                xysize (25, 20)
                                action Function(debug_reset_mission, mission["id"], True)
                                text "R" size 8 color "#ffffff" align (0.5, 0.5)

                # Side missions debug controls
                for mission in side_missions:
                    $ short_title = mission["title"][:20] + "..." if len(mission["title"]) > 20 else mission["title"]
                    vbox:
                        spacing 3
                        text short_title size 9 color "#C2C2C2"
                        hbox:
                            spacing 3
                            button:
                                background "#4CAF50"
                                hover_background "#66BB6A"
                                xysize (25, 20)
                                action Function(debug_activate_mission, mission["id"], False)
                                text "A" size 8 color "#ffffff" align (0.5, 0.5)
                            button:
                                background "#FF9800"
                                hover_background "#FFB74D"
                                xysize (25, 20)
                                action Function(debug_complete_mission, mission["id"], False)
                                text "C" size 8 color "#ffffff" align (0.5, 0.5)
                            button:
                                background "#F44336"
                                hover_background "#EF5350"
                                xysize (25, 20)
                                action Function(debug_fail_mission, mission["id"], False)
                                text "F" size 8 color "#ffffff" align (0.5, 0.5)
                            button:
                                background "#9E9E9E"
                                hover_background "#BDBDBD"
                                xysize (25, 20)
                                action Function(debug_reset_mission, mission["id"], False)
                                text "R" size 8 color "#ffffff" align (0.5, 0.5)

# New quest content section for side-by-side layout
screen quest_content_section(missions_filtered, is_main_mission):
    viewport:
        scrollbars "vertical"
        mousewheel True
        xfill True
        yfill True
        side_yfill True

        vbox:
            spacing 15
            if len(missions_filtered) == 0:
                frame:
                    background None
                    xfill True
                    ysize 120
                    padding (30, 30)
                    text "No quests available" size 16 font "fonts/Montserrat-Medium.ttf" color "#888888" align (0.5, 0.5)
            else:
                for mission in missions_filtered:
                    $ status_colors = {
                        MISSION_ACTIVE: "#2196F3",      # Blue for active
                        MISSION_OUTGOING: "#2196F3",    # Blue for outgoing
                        MISSION_COMPLETED: "#FF9800",   # Orange/Golden for completed
                        MISSION_FAILED: "#F44336"       # Red for failed
                    }
                    $ mission_color = status_colors.get(mission["status"], "#ffffff")
                    $ bg_alpha = "30" if mission["status"] == MISSION_COMPLETED else "20"

                    # Quest card with rounded corners and darker styling
                    if is_main_mission:
                        button:
                            background "#222222D0"  # Darker transparent background with rounded corners
                            hover_background "#333333D0"  # Darker transparent hover
                            xfill True
                            ysize 120
                            action Function(toggle_mission_expansion, mission["id"])
                            padding (20, 15)
                            at quest_button_hover

                            hbox:
                                spacing 15
                                xfill True
                                yalign 0.5

                                # Status indicator
                                frame:
                                    background mission_color
                                    xysize (4, 90)
                                    yalign 0.5

                                # Mission content
                                vbox:
                                    spacing 8
                                    xfill True
                                    yalign 0.5

                                    # Mission title with expansion indicator
                                    hbox:
                                        spacing 15
                                        xfill True

                                        text mission["title"] size 18 font "fonts/Montserrat-Black.ttf" color "#ffffff" yalign 0.5

                                        # Expansion indicator
                                        if mission.get("expanded", False):
                                            text "▼" size 12 color "#C2C2C2" xalign 1.0 yalign 0.5
                                        else:
                                            text "▶" size 12 color "#C2C2C2" xalign 1.0 yalign 0.5

                                    # Mission description
                                    text mission["description"] size 14 font "fonts/Montserrat-Medium.ttf" color "#C2C2C2" xfill True text_align 0.0

                        # Expanded details with animation
                        if mission.get("expanded", False):
                            frame:
                                background "#1f1f1fE0"  # Darker transparent background with rounded corners
                                xfill True
                                padding (20, 15)
                                at quest_expand

                                vbox:
                                    spacing 15

                                    # Mission image (16:9 aspect ratio)
                                    if mission.get("image"):
                                        frame:
                                            background "#0f0f0fF0"  # Darker transparent image frame with rounded corners
                                            xalign 0.5
                                            xysize (400, 225)  # 16:9 ratio
                                            padding (5, 5)

                                            add mission["image"]:
                                                fit "contain"
                                                xalign 0.5
                                                yalign 0.5

                                    # Details section
                                    vbox:
                                        spacing 10
                                        text "Quest Details" size 16 font "fonts/Montserrat-Black.ttf" color "#ffffff"
                                        text mission["details"] size 14 font "fonts/Montserrat-Medium.ttf" color "#C2C2C2" xfill True text_align 0.0
                    else:
                        # Side mission card (compact)
                        frame:
                            background "#222222D0"  # Darker transparent background with rounded corners
                            xfill True
                            ysize 100
                            padding (20, 15)
                            at quest_button_hover

                            hbox:
                                spacing 15
                                xfill True
                                yalign 0.5

                                # Status indicator
                                frame:
                                    background mission_color
                                    xysize (4, 70)
                                    yalign 0.5

                                # Small mission image (16:9 ratio)
                                if mission.get("image"):
                                    frame:
                                        background "#0f0f0fF0"  # Darker transparent image frame with rounded corners
                                        xysize (80, 45)  # 16:9 ratio
                                        yalign 0.5
                                        padding (3, 3)

                                        add mission["image"]:
                                            fit "contain"
                                            xalign 0.5
                                            yalign 0.5

                                # Mission content
                                vbox:
                                    spacing 8
                                    xfill True
                                    yalign 0.5

                                    # Mission title
                                    text mission["title"] size 16 font "fonts/Montserrat-Black.ttf" color "#ffffff"

                                    # Mission description
                                    text mission["description"] size 12 font "fonts/Montserrat-Medium.ttf" color "#C2C2C2" xfill True text_align 0.0

# Modern mission tab content screen
screen mission_tab_content_modern(main_missions_filtered, side_missions_filtered, tab_title):
    vbox:
        spacing 25
        xfill True
        yfill True

        # Content area with sophisticated layout
        hbox:
            spacing 30  # Reduced spacing to prevent overflow
            xfill True
            yfill True

            # Main Missions Section
            vbox:
                spacing 15
                xsize 600  # Reduced width to fit better
                yfill True

                # Main Missions Header with rounded corners
                frame:
                    background Frame("assets/ui/images/quest_frame.svg", 20, 20, tile=False)
                    xfill True
                    ysize 60
                    padding (25, 15)

                    hbox:
                        spacing 15
                        yalign 0.5
                        add "assets/ui/images/icon_quest.png" yalign 0.5
                        text "Primary Objectives" size 18 font "fonts/Montserrat-Black.ttf" color "#ffffff" yalign 0.5

                # Main Missions List with custom scrollbar
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    xfill True
                    yfill True
                    side_yfill True

                    vbox:
                        spacing 12
                        if len(main_missions_filtered) == 0:
                            frame:
                                background None
                                xfill True
                                ysize 120
                                padding (30, 30)
                                text "No primary objectives available" size 16 font "fonts/Montserrat-Medium.ttf" color "#888888" align (0.5, 0.5)
                        else:
                            for mission in main_missions_filtered:
                                $ status_colors = {
                                    MISSION_ACTIVE: "#2196F3",      # Blue for active
                                    MISSION_OUTGOING: "#2196F3",    # Blue for outgoing
                                    MISSION_COMPLETED: "#FF9800",   # Orange/Golden for completed
                                    MISSION_FAILED: "#F44336"       # Red for failed
                                }
                                $ mission_color = status_colors.get(mission["status"], "#ffffff")
                                $ bg_alpha = "25" if mission["status"] == MISSION_COMPLETED else "15"

                                # Mission card with rounded corners and animation
                                button:
                                    background Frame("assets/ui/images/quest_frame.svg", 20, 20, tile=False)
                                    hover_background Frame("assets/ui/images/quest_frame.svg", 20, 20, tile=False)
                                    xsize 570
                                    ysize 100
                                    action Function(toggle_mission_expansion, mission["id"])
                                    padding (25, 15)
                                    at quest_button_hover

                                    # Background color overlay
                                    frame:
                                        background f"#ffffff{bg_alpha}"
                                        xfill True
                                        yfill True

                                    hbox:
                                        spacing 20
                                        xfill True
                                        yalign 0.5

                                        # Status indicator with rounded corners
                                        frame:
                                            background Frame("assets/ui/images/quest_frame.svg", 3, 3, tile=False)
                                            xysize (6, 70)
                                            yalign 0.5

                                            frame:
                                                background mission_color
                                                xfill True
                                                yfill True

                                        # Mission content
                                        vbox:
                                            spacing 8
                                            xfill True
                                            yalign 0.5

                                            # Mission title with expansion indicator (single symbol)
                                            hbox:
                                                spacing 15
                                                xfill True

                                                text mission["title"] size 18 font "fonts/Montserrat-Black.ttf" color "#ffffff" yalign 0.5

                                                # Single symbol expansion indicator
                                                if mission.get("expanded", False):
                                                    text "▼" size 12 color "#C2C2C2" xalign 1.0 yalign 0.5
                                                else:
                                                    text "▶" size 12 color "#C2C2C2" xalign 1.0 yalign 0.5

                                            # Mission description
                                            text mission["description"] size 14 font "fonts/Montserrat-Medium.ttf" color "#C2C2C2" xfill True text_align 0.0

                                # Expanded details with animation and rounded corners
                                if mission.get("expanded", False):
                                    frame:
                                        background Frame("assets/ui/images/quest_frame.svg", 20, 20, tile=False)
                                        xsize 570
                                        padding (25, 20)
                                        at quest_expand

                                        # Background color overlay
                                        frame:
                                            background "#ffffff20"
                                            xfill True
                                            yfill True

                                        vbox:
                                            spacing 20

                                            # Mission image with rounded frame (16:9 aspect ratio)
                                            if mission.get("image"):
                                                frame:
                                                    background Frame("assets/ui/images/quest_frame.svg", 15, 15, tile=False)
                                                    xalign 0.5
                                                    xysize (400, 225)  # 16:9 ratio
                                                    padding (5, 5)

                                                    frame:
                                                        background "#000000AA"
                                                        xfill True
                                                        yfill True

                                                    add mission["image"]:
                                                        fit "contain"
                                                        xalign 0.5
                                                        yalign 0.5

                                            # Details section
                                            vbox:
                                                spacing 10
                                                text "Quest Details" size 16 font "fonts/Montserrat-Black.ttf" color "#ffffff"
                                                text mission["details"] size 14 font "fonts/Montserrat-Medium.ttf" color "#C2C2C2" xfill True text_align 0.0

            # Side Missions Section
            vbox:
                spacing 15
                xsize 560  # Reduced width to match primary objectives
                yfill True

                # Side Missions Header with rounded corners
                frame:
                    background Frame("assets/ui/images/quest_frame.svg", 20, 20, tile=False)
                    xfill True
                    ysize 60
                    padding (25, 15)

                    hbox:
                        spacing 15
                        yalign 0.5
                        add "assets/ui/images/icon_quest.png" yalign 0.5
                        text "Secondary Objectives" size 18 font "fonts/Montserrat-Black.ttf" color "#ffffff" yalign 0.5

                # Side Missions List with custom scrollbar
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    xfill True
                    yfill True
                    side_yfill True

                    vbox:
                        spacing 12
                        if len(side_missions_filtered) == 0:
                            frame:
                                background None
                                xfill True
                                ysize 120
                                padding (30, 30)
                                text "No secondary objectives available" size 16 font "fonts/Montserrat-Medium.ttf" color "#888888" align (0.5, 0.5)
                        else:
                            for mission in side_missions_filtered:
                                $ status_colors = {
                                    MISSION_ACTIVE: "#2196F3",      # Blue for active
                                    MISSION_OUTGOING: "#2196F3",    # Blue for outgoing
                                    MISSION_COMPLETED: "#FF9800",   # Orange/Golden for completed
                                    MISSION_FAILED: "#F44336"       # Red for failed
                                }
                                $ mission_color = status_colors.get(mission["status"], "#ffffff")
                                $ bg_alpha = "25" if mission["status"] == MISSION_COMPLETED else "15"

                                # Side mission card with rounded corners
                                frame:
                                    background Frame("assets/ui/images/quest_frame.svg", 20, 20, tile=False)
                                    xsize 535
                                    ysize 90
                                    padding (25, 15)
                                    at quest_button_hover

                                    # Background color overlay
                                    frame:
                                        background f"#ffffff{bg_alpha}"
                                        xfill True
                                        yfill True

                                    hbox:
                                        spacing 20
                                        xfill True
                                        yalign 0.5

                                        # Status indicator with rounded corners
                                        frame:
                                            background Frame("assets/ui/images/quest_frame.svg", 3, 3, tile=False)
                                            xysize (6, 60)
                                            yalign 0.5

                                            frame:
                                                background mission_color
                                                xfill True
                                                yfill True

                                        # Small mission image with rounded corners (16:9 ratio)
                                        if mission.get("image"):
                                            frame:
                                                background Frame("assets/ui/images/quest_frame.svg", 10, 10, tile=False)
                                                xysize (90, 50)  # 16:9 ratio
                                                yalign 0.5
                                                padding (3, 3)

                                                frame:
                                                    background "#000000AA"
                                                    xfill True
                                                    yfill True

                                                add mission["image"]:
                                                    fit "contain"
                                                    xalign 0.5
                                                    yalign 0.5

                                        # Mission content
                                        vbox:
                                            spacing 8
                                            xfill True
                                            yalign 0.5

                                            # Mission title
                                            text mission["title"] size 16 font "fonts/Montserrat-Black.ttf" color "#ffffff"

                                            # Mission description
                                            text mission["description"] size 12 font "fonts/Montserrat-Medium.ttf" color "#C2C2C2" xfill True text_align 0.0

# Modern mission screen styles
style mission_frame_modern:
    background "#ffffff15"
    padding (25, 20)

style mission_text_modern:
    font "fonts/Montserrat-Medium.ttf"
    color "#C2C2C2"

style mission_title_modern:
    font "fonts/Montserrat-Black.ttf"
    color "#ffffff"

style mission_button_modern:
    background "#ffffff15"
    hover_background "#ffffff25"
    padding (25, 15)

style mission_button_text_modern:
    font "fonts/Montserrat-SemiBold.ttf"
    color "#ffffff"
    hover_color "#C2C2C2"

style mission_header_modern:
    background "#ffffff15"
    padding (25, 15)

style mission_header_text_modern:
    font "fonts/Montserrat-Black.ttf"
    color "#ffffff"

style mission_status_indicator:
    background "#20ADA3"

style mission_card_modern:
    background "#ffffff15"
    hover_background "#ffffff25"

style mission_details_modern:
    background "#ffffff20"
    padding (25, 20)

style mission_image_frame_modern:
    background "#000000AA"
    padding (5, 5)

# Legacy styles for compatibility
style mission_frame:
    background Frame("gui/frame.png", 20, 20)
    padding (20, 20)

style mission_text:
    font "fonts/Montserrat-Medium.ttf"
    color "#ffffff"

style mission_button:
    background "#1a1a1a"
    hover_background "#2a2a2a"
    padding (10, 5)

style mission_button_text:
    font "fonts/Montserrat-SemiBold.ttf"
    color "#ffffff"
    hover_color "#cccccc"

style return_button:
    background Frame("gui/button/choice_idle_background.png", 10, 10)
    hover_background Frame("gui/button/choice_hover_background.png", 10, 10)
    xalign 0.5
    padding (20, 10)

style return_button_text:
    font "fonts/Montserrat-SemiBold.ttf"
    size 16
    color "#ffffff"
    hover_color "#cccccc"