init python:
    xpos = 1930
    txpos = 1930
    enable = 0

    stage = 1
    stage_max = 5

    mistake = 0
    mistake_max = 3

    abandon = False
    anomaly = False
    arrow = 0
    full = False

    start = True

init:
    transform blink:
        alpha 1.0
        linear 1.0 alpha 0.0
        linear 0.5 alpha 0.0
        linear 1.0 alpha 1.0
        linear 0.5 alpha 1.0
        repeat

    transform abandon_hover_alpha:
        on idle:
            linear 0.2 alpha 1.0
        on hover:
            linear 0.2 alpha 0.6

    transform text_alpha_controlled:
        alpha 1.0
        on show:
            linear 0.2 alpha 0.6
        on hover:
            linear 0.2 alpha 1.0

    # Custom dissolve fade transform for the sample image
    transform dissolve_fade:
        alpha 0.0
        linear 0.5 alpha 1.0
    transform dissolve_fade_static:
        alpha 0.0
        linear 0.5 alpha 0.0

    transform dissolve_fade_out:
        alpha 1.0
        linear 0.5 alpha 0.0
init python:
    # (INSERT ALL OF YOUR IMAGE HERE FOR THE RIGHT SIDE OF THE SCREEN)
    sample_masked = AlphaMask("gui/anomaly/sample2.png", "gui/anomaly/rounded_mask.png")

screen anomaly:

    timer 0.01 repeat True action If(txpos != xpos, true=SetVariable("xpos", xpos + int((txpos - xpos) * 0.05)))

    imagemap:
        ground "gui/anomaly/background.png"

    fixed:
        xpos xpos
        ypos 0

        # Slide Toggle
        imagemap:
            if enable == 0:
                idle "gui/anomaly/Toggle.png"
            else:
                idle "gui/anomaly/Toggle_2.png"
            xpos -50
            ypos 450
            hotspot (0, 0, 1920, 1080) clicked [
                SetVariable("txpos", 1090 if enable == 0 else 1930),
                SetVariable("enable", 1 - enable)
                ]
        # (MAKE SOME CONDITION HERE TO SHOW THE NEEDED IMAGES RIGHT SIDE OF THE SCREEN)
        add sample_masked

        add "gui/anomaly/rounded_border.png"
        imagebutton:
            idle "gui/anomaly/expand.png"
            xpos 0.02
            ypos 0.06
            action [SetVariable("full", not full),SetVariable("start", False)]

        # (IF THERE SOME HAPPENING HERE, MAKE SOME CONDITION TO SHOW THE NEEDED IMAGES RIGHT SIDE OF THE SCREEN)
        # Blink        
        imagebutton:
            xpos -70
            ypos 0.43
            at blink
            idle "gui/anomaly/circle.png"

    # Stage display
    imagemap:
        ground "gui/anomaly/top.png"
        xpos 0.02
        ypos 0.83
        text "Stage    " size 14 font "fonts/Montserrat-SemiBold.ttf" color "#c4c4c4" xpos 0.45 ypos 0.3 xanchor 0.5
        text "[stage]/[stage_max]" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#c4c4c4" xpos 0.87 ypos 0.3 xanchor 0.5
        imagemap:
            idle "gui/anomaly/game.png"
            xpos 0.1
            ypos 0.3

    # Mistake display
    imagemap:
        ground "gui/anomaly/top.png"
        xpos 0.02
        ypos 0.88
        text "Mistakes" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#c4c4c4" xpos 0.45 ypos 0.3 xanchor 0.5
        text "[mistake]/[mistake_max]" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#c4c4c4" xpos 0.87 ypos 0.3 xanchor 0.5
        imagemap:
            idle "gui/anomaly/x.png"
            xpos 0.12
            ypos 0.3

    # Abandon Button
    default abandon_hovered = False
    imagebutton:
        xpos 0.02
        ypos 0.93
        idle "gui/anomaly/top.png"
        hover "gui/anomaly/top.png"
        at abandon_hover_alpha
        action SetVariable("abandon", not abandon)
        hovered SetVariable("abandon_hovered", True)
        unhovered SetVariable("abandon_hovered", False)
        focus_mask True
    add Text("Abandon Raid", size=13, font="fonts/Montserrat-SemiBold.ttf", color="#ff3030"):
        xpos 0.063
        ypos 0.941
    add "gui/anomaly/Exit.png" xpos -20

    # Anomaly Button
    imagebutton:
        idle "gui/anomaly/anomaly.png"
        xpos 960 
        ypos 0.938
        xanchor 0.5
        at abandon_hover_alpha 
        action SetVariable("anomaly", not anomaly)

    text "Anomaly Found" size 14 font "fonts/Montserrat-SemiBold.ttf" color "#c4c4c4" xpos 960  ypos 0.949 xanchor 0.5
    

    # Arrow Button with increment/decrement hotspots
    imagemap:
        ground "gui/anomaly/raid_dots.png"
        xpos 910
        ypos 0.848

    imagebutton:
        idle "gui/anomaly/up.png"
        xpos 0.5
        ypos 0.84
        xanchor 0.5
        at abandon_hover_alpha 
        action If(arrow < 99, SetVariable("arrow", arrow + 1))


    imagebutton:
        idle "gui/anomaly/down.png"
        xpos 0.5
        ypos 0.89
        xanchor 0.5
        at abandon_hover_alpha 
        action If(arrow > 0, SetVariable("arrow", arrow - 1))


    frame:
        background "assets/ui/images/day_frame.svg"
        xysize (110, 48)
        xpos 31 ypos 31
        hbox:
            align (0.5, 0.5)
            spacing 5
            add "assets/ui/images/icon_day.png"
            text _("Day 33") size 14 font "fonts/Montserrat-SemiBold.ttf" yalign 0.5 color "#c4c4c4"

    # Time of day switcher - clean implementation with fixed positions
    button:
        background "assets/ui/images/time_frame.svg"
        hover_background Transform("assets/ui/images/time_frame.svg", matrixcolor=BrightnessMatrix(0.1))
        xysize (139, 48)
        xpos 151 ypos 31
        action Function(lambda: setattr(persistent, 'time_of_day', {'day': 'night', 'night': 'morning', 'morning': 'noon', 'noon': 'day'}[persistent.time_of_day]))
        tooltip _({"day": "Switch to night", "night": "Switch to morning", "morning": "Switch to noon", "noon": "Switch to day"}[persistent.time_of_day])
        
        # Icon (left side) with animation
        add "assets/ui/images/" + persistent.time_of_day + ".png":
            xpos 12  # Fixed distance from left edge
            yalign 0.5
            at time_switch
        
        # Line (center) - constant element, no animation needed
        add "assets/ui/images/line_time.png":
            xpos 40  # Fixed position between icon and text
            yalign 0.5
            
        # Text (right side) with animation
        text _({"day": "Day", "night": "Night", "morning": "Morning", "noon": "Noon"}[persistent.time_of_day]):
            xpos 50  # Fixed position after the line
            yalign 0.5
            size 14
            font "fonts/Montserrat-SemiBold.ttf"
            color "#c4c4c4"
            at time_switch
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7
        
    add "assets/ui/images/line_left.png" xpos 310 ypos 46 
    #----------------------------------------------
    use top()
    #----------------------------------------------

    button:
        background ("assets/ui/images/unmute_icon.svg" if persistent.is_muted else "assets/ui/images/mute_icon.svg")
        hover_background Transform(("assets/ui/images/unmute_icon.svg" if persistent.is_muted else "assets/ui/images/mute_icon.svg"), matrixcolor=BrightnessMatrix(0.1))
        xpos 1841 ypos 31
        xysize (48, 48)
        action [gui.toggle_mute, ToggleMute("music")]
        tooltip _(("Unmute" if persistent.is_muted else "Mute") + " audio")
        at transform:
            on idle:
                ease 0.2 alpha 1.0
            on hover:
                ease 0.2 alpha 0.7
        

    #FullScreen (THIS WILL BE THE FULLIMAGE SHOW)
    imagemap:
        idle "gui/anomaly/sample2.png" 
        if start:
            at dissolve_fade_static
        elif full:
            at dissolve_fade
        else:
            at dissolve_fade_out
    if full:
        imagebutton:
            idle "gui/anomaly/shrink.png"
            xpos 0.95
            ypos 0.04
            action SetVariable("full", not full)

    # Debug Section
    frame:
        xalign 0.04
        ypos 10000
        background "#0008"
        padding (10, 10)
        vbox:
            spacing 5
            text "DEBUG INFO" size 16 color "#c4c4c4"

            text "enable: [enable]" color "#c4c4c4" size 16 
            text "xpos: [xpos]" color "#c4c4c4" size 16 
            text "txpos: [txpos]" color "#c4c4c4" size 16 

            hbox:
                text "Stage: [stage]/[stage_max]" color "#c4c4c4" size 16 
                textbutton "-" action If(stage > 1, SetVariable("stage", stage - 1)) xsize 16 ysize 16
                textbutton "+" action If(stage < stage_max, SetVariable("stage", stage + 1)) xsize 16 ysize 16

            hbox:
                text "Max: [stage_max]" color "#c4c4c4" size 16 
                textbutton "-" action If(stage_max > 1, SetVariable("stage_max", stage_max - 1), If(stage > stage_max - 1, SetVariable("stage", stage_max - 1))) xsize 16  ysize 16
                textbutton "+" action SetVariable("stage_max", stage_max + 1) xsize 16  ysize 16

            hbox:
                text "Mistake: [mistake]/[mistake_max]" color "#c4c4c4" size 16 
                textbutton "-" action If(mistake > 0, SetVariable("mistake", mistake - 1)) xsize 16  ysize 16
                textbutton "+" action If(mistake < mistake_max, SetVariable("mistake", mistake + 1)) xsize 16 ysize 16

            hbox:
                text "Max: [mistake_max]" color "#c4c4c4" size 16 
                textbutton "-" action If(mistake_max > 1, SetVariable("mistake_max", mistake_max - 1), If(mistake > mistake_max - 1, SetVariable("mistake", mistake_max - 1))) xsize 16 ysize 16
                textbutton "+" action SetVariable("mistake_max", mistake_max + 1) xsize 16 ysize 16

            text "Abandon: [abandon]" color "#c4c4c4" size 16 
            text "Anomaly: [anomaly]" color "#c4c4c4" size 16 
            text "Arrow: [arrow]" color "#c4c4c4" size 16
